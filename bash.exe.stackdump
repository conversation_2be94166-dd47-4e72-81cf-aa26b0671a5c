Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9CF0, 0007FFFF8BF0) msys-2.0.dll+0x1FE8E
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210286019, 0007FFFF9BA8, 0007FFFF9CF0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9CF0  000210068E24 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9FD0  00021006A225 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEB0B40000 ntdll.dll
7FFEAEE00000 KERNEL32.DLL
7FFEAE4A0000 KERNELBASE.dll
7FFEAEA90000 USER32.dll
7FFEAE8A0000 win32u.dll
7FFEAED10000 GDI32.dll
7FFEADEB0000 gdi32full.dll
7FFEAE080000 msvcp_win.dll
7FFEAE130000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEAED40000 advapi32.dll
7FFEAEC60000 msvcrt.dll
7FFEAEF60000 sechost.dll
7FFEAE960000 RPCRT4.dll
7FFEAD160000 CRYPTBASE.DLL
7FFEAE400000 bcryptPrimitives.dll
7FFEAF860000 IMM32.DLL
