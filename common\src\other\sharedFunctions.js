import { firebase } from "../config/configureFirebase";
import { GetDistance } from "../other/GeoFunctions";
import { onValue, child, push, update } from "firebase/database";

export const formatBookingObject = async (bookingData, settings) => {
  console.log("formatBookingObject - bookingData recibido:", bookingData);

  const c = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const reference = [...Array(6)].map(_ => c[~~(Math.random()*c.length)]).join('');

  const { config } = firebase;
  let today;
  try{
      let res =  await fetch(`https://${config.projectId}.web.app/getservertime`, { method: 'GET', headers: {'Content-Type': 'application/json'}});
      const json = await res.json();
      if(json.time){
        today = json.time;
      } else{
        today = new Date().getTime();
      }
  }catch (err){
    today = new Date().getTime();
  }

  console.log("formatBookingObject - pickup:", bookingData.pickup);
  console.log("formatBookingObject - drop:", bookingData.drop);

  // Validar que existan las coordenadas antes de acceder a ellas
  if (!bookingData.pickup || !bookingData.pickup.coords) {
    throw new Error("Pickup location or coordinates missing");
  }
  if (!bookingData.drop || !bookingData.drop.coords) {
    throw new Error("Drop location or coordinates missing");
  }

  let pickUp = { lat: bookingData.pickup.coords.lat, lng: bookingData.pickup.coords.lng, add: bookingData.pickup.description };
  let drop = { lat: bookingData.drop.coords.lat, lng: bookingData.drop.coords.lng, add: bookingData.drop.description };

  // Se ha comentado la generación de OTP para deshabilitar la funcionalidad.
  // const otp = Math.floor(1000 + Math.random() * 9000).toString();
  const coords = [{
    lat: pickUp.lat,
    lng: pickUp.lng
  },
  {
    lat: drop.lat,
    lng: drop.lng
  }];

  return {
      carImage: bookingData.carDetails.image,
      carType: bookingData.carDetails.name,
      reference: reference,
      customer: bookingData.userDetails.uid,
      customer_image: bookingData.userDetails.profile_image ? bookingData.userDetails.profile_image : "",
      customer_email: bookingData.userDetails.email,
      customer_name: bookingData.userDetails.firstName + ' ' + bookingData.userDetails.lastName,
      nombreComercial: bookingData.userDetails.nombreComercial || "",
      customer_contact: bookingData.userDetails.mobile? bookingData.userDetails.mobile: ' ',
      customer_token: bookingData.userDetails.pushToken? bookingData.userDetails.pushToken: ' ',
      drop: drop,
      pickup: pickUp,
      estimate: bookingData.estimate.estimateFare,
      estimateDistance: bookingData.estimate.estimateDistance,
      distance: bookingData.estimate.estimateDistance,
      estimateTime:bookingData.estimate.estimateTime,
      status: bookingData.booking_type_admin || bookingData.deliveryWithBid? "NEW" : "PAYMENT_PENDING",
      bookLater:bookingData.bookLater,
      tripdate: bookingData.bookLater?bookingData.tripdate:today,
      bookingDate: today,
      // otp: otp, // Se ha comentado la inclusión de OTP para deshabilitar la funcionalidad.
      booking_type_admin:bookingData.booking_type_admin,
      coords: coords,
      waypoints: bookingData.drop.waypoints? bookingData.drop.waypoints: null,
      roundTrip: bookingData.roundTrip? bookingData.roundTrip:null,
      tripInstructions: bookingData.tripInstructions? bookingData.tripInstructions: null,
      trip_cost: bookingData.estimate.estimateFare,
      convenience_fees: bookingData.estimate.convenience_fees,
      driver_share: (parseFloat(bookingData.estimate.estimateFare) - parseFloat(bookingData.estimate.convenience_fees)).toFixed(settings.decimal),
      paymentPacket: bookingData.paymentPacket? bookingData.paymentPacket : null,
      preRequestedDrivers: bookingData.preRequestedDrivers?  bookingData.preRequestedDrivers: null,
      requestedDrivers: bookingData.requestedDrivers?  bookingData.requestedDrivers: null,
      driverEstimates: bookingData.driverEstimates?  bookingData.driverEstimates: null,
      ...bookingData.instructionData,
      // Asegurarse de que siempre haya un fleetadmin asignado para que sea visible para los administradores de flota
      fleetadmin: bookingData.fleetadmin? bookingData.fleetadmin:
                 (bookingData.userDetails && bookingData.userDetails.fleetadmin? bookingData.userDetails.fleetadmin : null),
      payment_mode: bookingData.payment_mode,
      fleet_admin_comission: bookingData.carDetails.fleet_admin_fee ? bookingData.carDetails.fleet_admin_fee : null,
      booking_from_web: bookingData.booking_from_web? bookingData.booking_from_web: false,
      deliveryWithBid: bookingData.deliveryWithBid ? bookingData.deliveryWithBid : false
  }
}

export const saveAddresses = async (booking, driverLocation) => {
  const { singleUserRef } = firebase;
  let address = booking.drop.add;

  onValue(child(singleUserRef(booking.customer), "savedAddresses"), (savedAdd) => {
      if (savedAdd.val()) {
        let addresses = savedAdd.val();
        let didNotMatch = true;
        for (let key in addresses) {
          let entry = addresses[key];
          if (
            GetDistance(
              entry.lat,
              entry.lng,
              booking.drop.lat,
              booking.drop.lng
            ) < 0.1
          ) {
            didNotMatch = false;
            let count = entry.count ? entry.count + 1 : 1;
            update(child(singleUserRef(booking.customer),"savedAddresses/" + key),{ count: count });
            break;
          }
        }
        if (didNotMatch) {
          push(child(singleUserRef(booking.customer),"savedAddresses"),{
            description: address,
            lat: booking.drop.lat,
            lng: booking.drop.lng,
            count: 1,
          });
        }
      } else {
        push(child(singleUserRef(booking.customer),"savedAddresses"),{
          description: address,
          lat: booking.drop.lat,
          lng: booking.drop.lng,
          count: 1,
        });
      }
    }, {onlyOnce: true});
  return address;
};

export const addActualsToBooking = async (booking, address, driverLocation) => {
  const end_time = new Date();
  const diff = (end_time.getTime() - parseFloat(booking.startTime)) / 1000;
  const totalTimeTaken = Math.abs(Math.round(diff));
  booking.trip_end_time = end_time.getHours() + ":" + end_time.getMinutes() + ":" + end_time.getSeconds();
  booking.endTime = end_time.getTime();
  booking.total_trip_time = totalTimeTaken;
  return booking;
};

export const updateDriverQueue = (booking) => {
  return booking;
};

export const driverQueue= true;