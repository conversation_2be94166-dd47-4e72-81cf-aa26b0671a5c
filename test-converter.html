<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>vert<PERSON><PERSON> <PERSON>tiples a Bookings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        select, input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🚚 Convertidor de Órdenes Múltiples a Bookings</h1>
    
    <div class="container">
        <h2>Configuración</h2>
        <div>
            <label>Endpoint URL:</label>
            <input type="text" id="endpointUrl" value="https://us-central1-balle-813e3.cloudfunctions.net/createBooking" style="width: 500px;">
        </div>
        <div>
            <label>Tipo de Pago por Defecto:</label>
            <select id="paymentType">
                <option value="EFECTIVO">EFECTIVO</option>
                <option value="TRANSFERENCIA">TRANSFERENCIA</option>
                <option value="TARJETA">TARJETA</option>
            </select>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>📥 Body de Órdenes Múltiples</h2>
            <textarea id="multiOrderInput" placeholder="Pega aquí tu JSON de órdenes múltiples..."></textarea>
            <button onclick="convertToBookings()">🔄 Convertir a Bookings</button>
            <button onclick="loadExample()">📋 Cargar Ejemplo</button>
        </div>

        <div class="container">
            <h2>📤 Bookings Generados</h2>
            <textarea id="bookingsOutput" readonly placeholder="Los bookings convertidos aparecerán aquí..."></textarea>
            <button onclick="createBookings()">🚀 Crear Reservas</button>
            <button onclick="copyBookings()">📋 Copiar JSON</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 Resultados</h2>
        <div id="results"></div>
    </div>

    <script>
        // Función para mapear tipos de vehículo
        function mapVehicleType(vehicleType) {
            const vehicleMap = {
                'CAMIONETA': 'VAN',
                'SEDAN': 'SEDAN',
                'MINI VAN': 'MINI VAN',
                'VAN': 'VAN',
                'PREMIER': 'PREMIER'
            };
            return vehicleMap[vehicleType] || 'SEDAN';
        }

        // Función para convertir una orden a booking
        function convertOrderToBooking(order, vehicleType, defaultPaymentType) {
            return {
                cliente: order.customerId,
                solicitante: order.customerName,
                ubicacionOrigen: {
                    direccion: order.pickupAddress.address
                },
                ubicacionDestino: {
                    direccion: order.deliveryAddress.address
                },
                tipoVehiculo: mapVehicleType(vehicleType),
                tipoPago: defaultPaymentType,
                telefonoContacto: order.customerPhone || null,
                observaciones: order.notes || null,
                nombrePasajero: order.customerName,
                numeroPasajeros: 1
            };
        }

        // Función principal de conversión
        function convertMultiOrderToBookings(multiOrderBody, defaultPaymentType) {
            const bookings = [];
            
            if (!multiOrderBody.orders || !Array.isArray(multiOrderBody.orders)) {
                throw new Error('El body debe contener un array de orders');
            }
            
            if (!multiOrderBody.vehicle || !multiOrderBody.vehicle.type) {
                throw new Error('El body debe contener información del vehículo');
            }
            
            multiOrderBody.orders.forEach((order, index) => {
                const booking = convertOrderToBooking(order, multiOrderBody.vehicle.type, defaultPaymentType);
                
                if (multiOrderBody.folio) {
                    booking.observaciones = booking.observaciones 
                        ? `${booking.observaciones} | Folio: ${multiOrderBody.folio} | Orden: ${index + 1}`
                        : `Folio: ${multiOrderBody.folio} | Orden: ${index + 1}`;
                }
                
                bookings.push(booking);
            });
            
            return bookings;
        }

        // Cargar ejemplo
        function loadExample() {
            const example = {
                "folio": "ENT-2024-TEST-001",
                "driver": "-OSgEoZzTKd3WlZ-xRyY",
                "vehicle": {
                    "type": "CAMIONETA",
                    "plate": "TEST-123"
                },
                "orders": [
                    {
                        "customerId": "VeSkwck41kXpdJIoDQH4sMjYror1",
                        "customerName": "María García Test",
                        "customerPhone": "+************",
                        "pickupAddress": {
                            "address": "Almacén Central - Dirección de Prueba"
                        },
                        "deliveryAddress": {
                            "address": "Destino Final - Dirección de Prueba"
                        },
                        "notes": "Entrega de prueba - manejar con cuidado"
                    },
                    {
                        "customerId": "test_customer_uid_789",
                        "customerName": "Juan Pérez Test",
                        "customerPhone": "+************",
                        "pickupAddress": {
                            "address": "Segundo Punto de Recogida"
                        },
                        "deliveryAddress": {
                            "address": "Segundo Destino Final"
                        },
                        "notes": "Segunda entrega de prueba"
                    }
                ]
            };
            
            document.getElementById('multiOrderInput').value = JSON.stringify(example, null, 2);
        }

        // Convertir a bookings
        function convertToBookings() {
            try {
                const input = document.getElementById('multiOrderInput').value;
                const paymentType = document.getElementById('paymentType').value;
                
                if (!input.trim()) {
                    showError('Por favor ingresa un JSON válido');
                    return;
                }
                
                const multiOrderBody = JSON.parse(input);
                const bookings = convertMultiOrderToBookings(multiOrderBody, paymentType);
                
                document.getElementById('bookingsOutput').value = JSON.stringify(bookings, null, 2);
                showSuccess(`✅ Se generaron ${bookings.length} bookings exitosamente`);
                
            } catch (error) {
                showError(`❌ Error: ${error.message}`);
            }
        }

        // Crear reservas
        async function createBookings() {
            try {
                const bookingsText = document.getElementById('bookingsOutput').value;
                const endpointUrl = document.getElementById('endpointUrl').value;
                
                if (!bookingsText.trim()) {
                    showError('Primero convierte las órdenes a bookings');
                    return;
                }
                
                const bookings = JSON.parse(bookingsText);
                const results = [];
                
                showInfo(`🚀 Creando ${bookings.length} reservas...`);
                
                for (let i = 0; i < bookings.length; i++) {
                    const booking = bookings[i];
                    
                    try {
                        const response = await fetch(endpointUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(booking)
                        });
                        
                        const result = await response.json();
                        
                        results.push({
                            orderIndex: i + 1,
                            success: response.ok,
                            status: response.status,
                            data: result,
                            customerName: booking.solicitante
                        });
                        
                        // Pausa entre llamadas
                        await new Promise(resolve => setTimeout(resolve, 500));
                        
                    } catch (error) {
                        results.push({
                            orderIndex: i + 1,
                            success: false,
                            error: error.message,
                            customerName: booking.solicitante
                        });
                    }
                }
                
                showResults(results);
                
            } catch (error) {
                showError(`❌ Error: ${error.message}`);
            }
        }

        // Copiar bookings
        function copyBookings() {
            const output = document.getElementById('bookingsOutput');
            output.select();
            document.execCommand('copy');
            showSuccess('📋 JSON copiado al portapapeles');
        }

        // Mostrar resultados
        function showResults(results) {
            const successful = results.filter(r => r.success);
            const failed = results.filter(r => !r.success);
            
            let html = `
                <div class="result">
                    <h3>📊 Resumen de Resultados</h3>
                    <p>✅ Exitosas: ${successful.length}</p>
                    <p>❌ Fallidas: ${failed.length}</p>
                    <p>📋 Total: ${results.length}</p>
                </div>
            `;
            
            if (successful.length > 0) {
                html += '<div class="success"><h4>✅ Reservas Exitosas:</h4><ul>';
                successful.forEach(result => {
                    const ref = result.data?.data?.reference || 'Sin referencia';
                    html += `<li>Orden ${result.orderIndex}: ${result.customerName} (${ref})</li>`;
                });
                html += '</ul></div>';
            }
            
            if (failed.length > 0) {
                html += '<div class="error"><h4>❌ Reservas Fallidas:</h4><ul>';
                failed.forEach(result => {
                    const error = result.error || result.data?.error || 'Error desconocido';
                    html += `<li>Orden ${result.orderIndex}: ${result.customerName} - ${error}</li>`;
                });
                html += '</ul></div>';
            }
            
            document.getElementById('results').innerHTML = html;
        }

        // Funciones de utilidad para mostrar mensajes
        function showSuccess(message) {
            document.getElementById('results').innerHTML = `<div class="success">${message}</div>`;
        }

        function showError(message) {
            document.getElementById('results').innerHTML = `<div class="error">${message}</div>`;
        }

        function showInfo(message) {
            document.getElementById('results').innerHTML = `<div class="result">${message}</div>`;
        }
    </script>
</body>
</html>
