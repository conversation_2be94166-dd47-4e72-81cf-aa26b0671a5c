import React, { useState, useEffect } from "react";
import { <PERSON>po<PERSON>, Grid, Card, Avatar, Button, Box } from "@mui/material";
import { useParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { colors } from "../components/Theme/WebTheme";
import moment from "moment/min/moment-with-locales";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import { makeStyles } from "@mui/styles";
import { MAIN_COLOR, SECONDORY_COLOR, FONT_FAMILY } from "../common/sharedFunctions";
import { api } from "common";

const useStyles = makeStyles((theme) => ({
  card: {
    borderRadius: "10px",
    backgroundColor: colors.WHITE,
    minHeight: 100,
    marginTop: 5,
    marginBottom: 20,
    boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
  },
  txt: {
    padding: 10,
    fontWeight: "bold",
    minHeight: 60,
    backgroundColor: SECONDORY_COLOR,
    color: colors.BLACK,
    boxShadow: 3,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
}));

function BookingDetails() {
  const { id } = useParams();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir();
  const bookinglistdata = useSelector((state) => state.bookinglistdata);
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const settings = useSelector((state) => state.settingsdata.settings);
  const classes = useStyles();
  const role = useSelector(state => state.auth.profile.usertype);
  const booking = bookinglistdata.bookings.find(item => item.id === id);

  // Registrar traducciones necesarias
  const translations = {
    'Nombre Comercial': 'Nombre Comercial',
    'no_commercial_name': 'Sin nombre comercial',
    'delivery_folio': 'Folio de Delivery',
    'order_count': 'Número de Órdenes',
    'delivery_orders': 'Órdenes de Delivery',
    'delivery_route': 'Ruta de Delivery',
    'order_details': 'Detalles de Órdenes',
    'no_delivery_info': 'Sin información de delivery',
    'delivery_info': 'Información de Delivery',
    'order_id': 'ID de Orden',
    'customer_name': 'Nombre del Cliente',
    'pickup_address': 'Dirección de Recogida',
    'delivery_address': 'Dirección de Entrega',
    'product_count': 'Cantidad de Productos'
  };

  // Registrar las traducciones
  Object.keys(translations).forEach(key => {
    if (!i18n.exists(key)) {
      i18n.addResource('es', 'translation', key, translations[key]);
    }
  });

  const usersdata = useSelector((state) => state.usersdata);
  const dispatch = useDispatch();
  const { fetchUsersOnce } = api;

  // Cargar datos de usuarios
  useEffect(() => {
    if (!usersdata.users) {
      dispatch(fetchUsersOnce());
    }
  }, [dispatch, fetchUsersOnce, usersdata.users]);

  useEffect(() => {
    if (bookinglistdata.bookings) {
      const booking = bookinglistdata.bookings.find(
        (item) => item.id === id.toString()
      );

      if (booking) {
        console.log('Booking data:', booking);
        console.log('nombreComercial:', booking.nombreComercial);
        console.log('customer_business_name:', booking.customer_business_name);
        console.log('customer:', booking.customer);

        // Buscar el usuario correspondiente para obtener el nombreComercial
        let userNombreComercial = "";
        if (booking.customer && usersdata.users) {
          const customerUser = usersdata.users.find(user => user.id === booking.customer);
          if (customerUser) {
            console.log('Customer user found:', customerUser);
            userNombreComercial = customerUser.nombreComercial || "";
            console.log('User nombreComercial:', userNombreComercial);
          }
        }

        const formattedBooking = {
          ...booking,
          // Campos financieros formateados
          driver_share: booking.driver_share && !isNaN(parseFloat(booking.driver_share))
            ? parseFloat(booking.driver_share).toFixed(2)
            : "0.00",
          convenience_fees: booking.convenience_fees ? parseFloat(booking.convenience_fees).toFixed(2) : "0.00",
          trip_cost: booking.trip_cost ? parseFloat(booking.trip_cost).toFixed(2) : "0.00",
          discount: booking.discount ? parseFloat(booking.discount).toFixed(2) : "0.00",

          // Datos del cliente - Priorizar el nombreComercial del usuario si está disponible
          nombreComercial: userNombreComercial || booking.nombreComercial || booking.customer_business_name || "",
          customer_business_name: booking.customer_business_name || booking.nombreComercial || userNombreComercial || "",

          // Campos del pasajero
          nombrePasajero: booking.passengerName || booking.nombrePasajero || "",
          telefonoContacto: booking.passengerContact || booking.telefonoContacto || "",
          numeroPasajeros: booking.numPassengers || booking.numeroPasajeros ?
            parseInt(booking.numPassengers || booking.numeroPasajeros) : null,
          numeroVale: booking.voucherNumber || booking.numeroVale || "",
          observaciones: booking.tripInstructions || booking.observaciones || "",

          // Información del aeropuerto
          tipoViaje: booking.flightInfo ? "AEROPUERTO" : (booking.tipoViaje || "NORMAL"),
          aerolinea: booking.flightInfo?.airline || booking.aerolinea || "",
          numeroVuelo: booking.flightInfo?.flightNumber || booking.numeroVuelo || "",
          horaLlegada: booking.flightInfo?.arrivalTime || booking.horaLlegada || ""
        };
        setData(formattedBooking);
      } else {
        navigate("/404");
        setData([]);
      }
    }
  }, [bookinglistdata.bookings, id, navigate]);

  const renderGridItem = (item, isRTL) => {
    return (
      <Grid
        key={item.key}
        container
        spacing={1}
        sx={{ direction: isRTL === "rtl" ? "rtl" : "ltr" }}
        style={{
          justifyContent: "center",
          alignItems: "center",
          minHeight: 60,
          ...item.addressGridStyle
        }}
      >
        <Grid item xs={4}>
          <Typography
            style={{
              fontSize: 16,
              padding: 2,
              textAlign: isRTL === "rtl" ? "right" : "left",
              fontFamily:FONT_FAMILY,
              ...item.typographyStyleAddress
            }}
          >
            {item.label}
          </Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography
            style={{
              fontSize: 18,
              padding: 2,
              letterSpacing: -1,
              textAlign: "center",
              fontFamily:FONT_FAMILY,
            }}
          >
            -----
          </Typography>
        </Grid>
        <Grid item xs={4}>
          <Typography
            style={{
              fontSize: 16,
              padding: 2,
              textAlign: isRTL === "rtl" ? "left" : "right",
              wordBreak: 'break-word',
              fontFamily:FONT_FAMILY,
              ...item.style,
            }}
          >
            {item.value}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  const renderTypography = (text) => {
    return (
      <Typography
      className={classes.txt}
      style={{
        borderBottomRightRadius: isRTL ? "90px" : "",
        borderBottomLeftRadius: isRTL ? "" : "90px",
        fontFamily:FONT_FAMILY,
      }}
      variant="h5"
      >
        {text}
      </Typography>
    );
  };

  const renderWaypoints = () => {
    if (booking && booking.waypoints && booking.waypoints.length > 0) {
        return booking.waypoints.map((point, index) => (
            <Typography key={index} className={classes.detailItem}>
                {`Punto ${String.fromCharCode(65 + index)}: ${point.add}`}
            </Typography>
        ));
    }
    return null;
  };

  const renderDeliveryOrders = () => {
    if (!data?.delivery_orders || !Array.isArray(data.delivery_orders)) {
      return null;
    }

    return data.delivery_orders.map((order, index) => (
      <Grid
        key={`order-${index}`}
        container
        spacing={1}
        sx={{
          direction: isRTL === "rtl" ? "rtl" : "ltr",
          marginBottom: 2,
          padding: 1,
          backgroundColor: colors.LIGHT_GRAY,
          borderRadius: 1,
          border: `1px solid ${SECONDORY_COLOR}`
        }}
      >
        <Grid item xs={12}>
          <Typography
            style={{
              fontSize: 14,
              fontWeight: "bold",
              color: MAIN_COLOR,
              fontFamily: FONT_FAMILY,
              marginBottom: 5
            }}
          >
            {`${t("Detalles")} ${index + 1}`}
          </Typography>
        </Grid>

        {/* ID de Orden */}
        {order.orderId && (
          <Grid item xs={12}>
            <Typography style={{ fontSize: 12, fontFamily: FONT_FAMILY }}>
              <strong>{t("order_id")}:</strong> {order.orderId}
            </Typography>
          </Grid>
        )}

        {/* Nombre del Cliente */}
        {order.customerName && (
          <Grid item xs={12}>
            <Typography style={{ fontSize: 12, fontFamily: FONT_FAMILY }}>
              <strong>{t("customer_name")}:</strong> {order.customerName}
            </Typography>
          </Grid>
        )}

        {/* Dirección de Pickup */}
        {order.pickupAddress && (
          <Grid item xs={12}>
            <Typography style={{ fontSize: 12, fontFamily: FONT_FAMILY, color: colors.GREEN }}>
              <strong>{t("pickup_address")}:</strong> {order.pickupAddress}
            </Typography>
          </Grid>
        )}

        {/* Dirección de Delivery */}
        {order.deliveryAddress && (
          <Grid item xs={12}>
            <Typography style={{ fontSize: 12, fontFamily: FONT_FAMILY, color: colors.RED }}>
              <strong>{t("Direccion de Entrega")}:</strong> {order.deliveryAddress}
            </Typography>
          </Grid>
        )}

        {/* Cantidad de Productos */}
        {order.productCount && (
          <Grid item xs={12}>
            <Typography style={{ fontSize: 12, fontFamily: FONT_FAMILY }}>
              <strong>{t("Cantidad de Productos")}:</strong> {order.productCount}
            </Typography>
          </Grid>
        )}
      </Grid>
    ));
  };

  return (
    <>
      <div dir={isRTL === "rtl" ? "rtl" : "ltr"}
        style={{
          marginBottom: 20,
        }}
      >
        <Button
          variant="text"
          onClick={() => {
            navigate("/bookings");
          }}
        >
          <Typography
            style={{
              margin: "10px 10px 0 5px",
              textAlign: isRTL === "rtl" ? "right" : "left",
              fontWeight: "bold",
              color: MAIN_COLOR,
              fontFamily:FONT_FAMILY,
            }}
          >
            {`<<- ${t("go_back")}`}
          </Typography>
        </Button>
      </div>
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
              {renderTypography(t("ride_information"))}
                <Grid container direction="column" style={{ padding: 10 }}>
                  {data &&
                    [
                      // Información básica de la reserva
                      {
                        key: "booking_ref",
                        label: t("booking_ref"),
                        value: data.reference,
                        style: { fontWeight: "bold" }
                      },
                      { key: "booking_id", label: t("booking_id"), value: data.id },
                      {
                        key: "booking_status_web",
                        label: t("booking_status_web"),
                        value: t(data.status),
                        style: {
                          backgroundColor: data?.status === "CANCELLED" ? colors.RED : data?.status === "COMPLETE" ? colors.GREEN : colors.YELLOW,
                          color: colors.WHITE,
                          fontWeight: "bold",
                          borderRadius: "10px",
                          textAlign: "center",
                          padding: 3,
                        },
                      },
                      {
                        key: "trip_start_date",
                        label: "Fecha de inicio",
                        value: data?.tripdate ? moment(data?.tripdate).format("lll") : ""
                      },
                      { key: "trip_start_time", label: t("trip_start_time"), value: data?.trip_start_time },
                      { key: "trip_end_time", label: t("trip_end_time"), value: data?.trip_end_time },
                      { key: "total_time", label: t("total_time"), value: data?.total_trip_time },

                      // Información del solicitante y pasajero
                      {
                        key: "solicitante",
                        label: t("Solicitante"),
                        value: data?.solicitante || "",
                        style: { fontWeight: "bold" }
                      },
                    

                      // Detalles del viaje
                      {
                        key: "payment_mode_web",
                        label: t("Tipo de pago"),
                        value: data?.payment_mode || t("Sin tipo de pago")
                      },
                      
                      {
                        key: "tipoViaje",
                        label: t("Tipo de viaje"),
                        value: data?.tipoViaje || "NORMAL",
                        style: { fontWeight: "bold" }
                      },

                      // Información de Delivery (condicional)
                      ...((data?.delivery_type === 'MULTIPLE_ORDERS' || data?.delivery_orders || data?.delivery_folio) ? [
                        {
                          key: "delivery_folio",
                          label: t("Folio"),
                          value: data?.delivery_folio || t("no_delivery_info"),
                          style: {
                            color: data?.delivery_folio ? colors.BLACK : colors.RED,
                            fontWeight: data?.delivery_folio ? 'bold' : 'normal',
                            fontStyle: data?.delivery_folio ? 'normal' : 'italic',
                            backgroundColor: data?.delivery_folio ? colors.LIGHT_BLUE : colors.LIGHT_GRAY,
                            padding: '4px 8px',
                            borderRadius: '6px',
                            border: `2px solid ${data?.delivery_folio ? colors.BLUE : colors.RED}`
                          }
                        },
                        {
                          key: "order_count",
                          label: t("Ordenes"),
                          value: data?.order_count ? data.order_count.toString() : (data?.delivery_orders?.length || 0).toString(),
                          style: {
                            fontWeight: "bold",
                            color: colors.BLUE,
                            backgroundColor: colors.WHITE,
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: `1px solid ${colors.BLUE}`
                          }
                        },
                        {
                          key: "delivery_route_id",
                          label: t("Ruta"),
                          value: data?.delivery_route_id || t("no_delivery_info"),
                          style: {
                            color: data?.delivery_route_id ? colors.BLACK : colors.RED,
                            fontStyle: data?.delivery_route_id ? 'normal' : 'italic',
                            fontSize: '12px'
                          }
                        }
                      ] : []),
                      {
                        key: "pickup_address",
                        label: t("pickup_address"),
                        value: data?.pickupAddress,
                        addressGridStyle: {
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          flexDirection: "row",
                          minHeight: 60,
                          marginBottom: 20,
                        },
                        typographyStyleAddress: { color: colors.GREEN }
                      },
                      {
                        key: "drop_address",
                        label: t("drop_address"),
                        value: data?.dropAddress,
                        typographyStyleAddress: { color: colors.RED }
                      },
                      {
                        key: "distance_web",
                        label: t("distance_web"),
                        value: data.distance ? (settings.convert_to_mile ? data?.distance + t("mile") : data?.distance + " " + t("km")) : ""
                      },

                      // Información específica del aeropuerto (condicional)
                      ...((data?.tipoViaje === "AEROPUERTO" || data?.flightInfo) ? [
                        {
                          key: "aerolinea",
                          label: t("Aerolinea"),
                          value: data?.aerolinea || t("Sin aerolinea"),
                          style: {
                            color: data?.aerolinea ? colors.BLACK : colors.RED,
                            fontWeight: 'normal',
                            fontStyle: data?.aerolinea ? 'normal' : 'italic',
                            backgroundColor: colors.WHITE,
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: `1px solid ${data?.aerolinea ? colors.BLACK : colors.RED}`
                          }
                        },
                        {
                          key: "numeroVuelo",
                          label: t("Numero de vuelo"),
                          value: data?.numeroVuelo || t("Sin numero de vuelo"),
                          style: {
                            color: data?.numeroVuelo ? colors.BLACK : colors.RED,
                            fontWeight: 'normal',
                            fontStyle: data?.numeroVuelo ? 'normal' : 'italic',
                            backgroundColor: colors.WHITE,
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: `1px solid ${data?.numeroVuelo ? colors.BLACK : colors.RED}`
                          }
                        },
                        {
                          key: "horaLlegada",
                          label: t("Hora de llegada"),
                          value: data?.horaLlegada || t("Sin hora de llegada"),
                          style: {
                            color: data?.horaLlegada ? colors.BLACK : colors.RED,
                            fontWeight: 'normal',
                            fontStyle: data?.horaLlegada ? 'normal' : 'italic',
                            backgroundColor: colors.WHITE,
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: `1px solid ${data?.horaLlegada ? colors.BLACK : colors.RED}`
                          }
                        },
                      ] : []),

                      // Instrucciones y notas
                      { key: "tripInstructions", label: t("tripInstructions"), value: data?.tripInstructions },
                      { key: "pickUpInstructions_web", label: t("pickUpInstructions_web"), value: data?.pickUpInstructions },
                      { key: "deliveryInstructions", label: t("deliveryInstructions"), value: data?.deliveryInstructions },
                      { key: "otherPerson", label: t("otherPerson"), value: data?.otherPerson },
                      { key: "otherPersonPhone", label: t("otherPersonPhone"), value: data?.otherPersonPhone },
                      { key: "feedback", label: t("feedback"), value: data?.feedback },
                      { key: "cancellation_reason", label: t("cancellation_reason"), value: data?.reason },

                      // Imágenes
                      { key: "take_pickup_image_web", label: t("take_pickup_image_web"), value: data.pickup_image ? data.pickup_image : "", type: 'image', alt: "pickup_image" },
                      { key: "take_deliver_image_web", label: t("take_deliver_image_web"), value: data.deliver_image ? data.deliver_image : "", type: 'image', alt: "deliver_image" },
                    ].map((item) =>
                      item.type === "image" && item.value ? (
                        <Grid
                          key={item.key}
                          container
                          spacing={1}
                          sx={{ direction: isRTL === "rtl" ? "rtl" : "ltr" }}
                          style={{
                            justifyContent: "center",
                            alignItems: "center",
                            minHeight: 60,
                          }}
                        >
                          <Grid item xs={4}>
                            <Typography
                              style={{
                                fontSize: 16,
                                padding: 2,
                                textAlign: isRTL === "rtl" ? "right" : "left",
                                fontFamily:FONT_FAMILY,
                              }}
                            >
                              {item.label}
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Typography
                              style={{
                                fontSize: 18,
                                padding: 2,
                                letterSpacing: -1,
                                textAlign: "center",
                                fontFamily:FONT_FAMILY,
                              }}
                            >
                              -----
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <img
                              src={item.value}
                              alt={item.alt}
                              style={{
                                width: 120,
                                height: 90,
                              }}
                            />
                          </Grid>
                        </Grid>
                      ) :
                        item.value ?renderGridItem(item, isRTL) : null
                    )}
                </Grid>

                {/* Sección de Órdenes de Delivery */}
                {(data?.delivery_orders && data.delivery_orders.length > 0) && (
                  <Grid container direction="column" style={{ padding: 15, marginTop: 10 }}>
                    <Typography
                      style={{
                        fontSize: 16,
                        fontWeight: "bold",
                        color: MAIN_COLOR,
                        fontFamily: FONT_FAMILY,
                        marginBottom: 10,
                        textAlign: "center"
                      }}
                    >
                      {t("Pedidos")}
                    </Typography>
                    {renderDeliveryOrders()}
                  </Grid>
                )}
              </Card>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("payment_info"))}
                <Grid container direction="column" style={{ paddingRight: 15, paddingLeft: 15, paddingBottom: 15 }}>
                  {data &&
                    [
                      {
                        key: "trip_cost",
                        label: t("trip_cost"),
                        value: data?.trip_cost ? (settings.swipe_symbol
                          ? data.trip_cost + " " + settings.symbol
                          : settings.symbol + " " + data.trip_cost
                        ) : "0.00"
                      },
                      {
                        key: "Customer_paid", label: t("Customer_paid"), value: data?.customer_paid ? (settings.swipe_symbol
                            ? data?.customer_paid + " " + settings.symbol
                            : settings.symbol + " " + data?.customer_paid) : ""
                      },
                      {
                        key: "discount_ammount",
                        label: t("discount_ammount"),
                        value: settings.swipe_symbol
                          ? data?.discount + " " + settings.symbol
                          : settings.symbol + " " + data?.discount,
                      },
                      {
                        key: "driver_share",
                        label: t("driver_share"),
                        value: data?.driver_share && !isNaN(parseFloat(data.driver_share)) ? (
                          settings.swipe_symbol
                            ? `${data.driver_share} ${settings.symbol}`
                            : `${settings.symbol} ${data.driver_share}`
                        ) : `${settings.symbol} 0.00`
                      },
                      {
                        key: "fleet_admin_comission", label: t("fleet_admin_comission"), value: data?.fleetCommission && parseFloat(data?.fleetCommission) > 0 ? (settings.swipe_symbol
                          ? data?.fleetCommission + " " + settings.symbol
                          : settings.symbol + " " + data?.fleetCommission) : ""
                      },
                      {
                        key: "convenience_fee",
                        label: t("convenience_fee"),
                        value: data?.convenience_fees ? (settings.swipe_symbol
                          ? data.convenience_fees + " " + settings.symbol
                          : settings.symbol + " " + data.convenience_fees
                        ) : "0.00"
                      },
                      {
                        key: "cancellationFee", label: t("cancellationFee"), value: settings.swipe_symbol
                          ? data.cancellationFee
                            ? data.cancellationFee
                            : (0).toFixed(settings.decimal) +
                            " " +
                            settings.symbol
                          : settings.symbol + " " + data.cancellationFee
                            ? data.cancellationFee
                            : (0).toFixed(settings.decimal)
                      },
                      {
                        key: "payment_gateway", label: t("payment_gateway"), value: data?.gateway
                      },
                      {
                        key: "cash_payment_amount", label: t("cash_payment_amount"), value: data?.cashPaymentAmount ? (settings.swipe_symbol
                          ? data?.cashPaymentAmount + " " + settings.symbol
                          : settings.symbol + " " + data?.cashPaymentAmount) : ""
                      },
                      {
                        key: "card_payment_amount", label: t("card_payment_amount"), value:
                          data?.cardPaymentAmount ? (settings.swipe_symbol
                            ? data?.cardPaymentAmount + " " + settings.symbol
                            : settings.symbol + " " + data?.cardPaymentAmount) : ""
                      },
                      {
                        key: "wallet_payment_amount", label: t("wallet_payment_amount"), value:
                          data?.usedWalletMoney ? (settings.swipe_symbol
                            ? data?.usedWalletMoney + " " + settings.symbol
                            : settings.symbol + " " + data?.usedWalletMoney) : ""
                      },
                      {
                        key: "payable_ammount", label: t("payable_ammount"), value: data?.payableAmount ? (settings.swipe_symbol
                          ? data?.payableAmount + " " + settings.symbol
                          : settings.symbol + " " + data?.payableAmount) : ""
                      },
                    ].map((item) =>
                      item.value ? renderGridItem(item, isRTL): null
                    )}
                </Grid>
              </Card>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("driver_info"))}
                <Grid container direction="column" style={{ padding: 15 }}>
                  <Grid
                    item
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {data?.driver_image ? (
                      <Avatar
                        alt="driver profile image"
                        src={data?.driver_image}
                        style={{ width: 100, height: 100, objectFit: "cover" }}
                      />
                    ) : (
                      <AccountCircleIcon sx={{ width: 100, height: 100 }} />
                    )}
                  </Grid>
                  {data &&
                    [
                      { key: "driver_id", label: t("driver_id"), value: data?.driver ? data.driver : "" },
                      { key: "driver_name", label: t("driver_name"), value: data?.driver_name ? data.driver_name : "" },
                      {
                        key: "driver_contact", label: t("driver_contact"), value: data?.driver_contact ? (settings.AllowCriticalEditsAdmin
                          ? data?.driver_contact
                          : t("hidden_demo")) : ""
                      },
                      {
                        key: "driver_email", label: t("driver_email"), value: data?.driver_email ? (settings.AllowCriticalEditsAdmin
                          ? data?.driver_email
                          : t("hidden_demo")) : ""
                      },
                      { key: "car_type", label: t("car_type"), value: data?.carType ? data.carType : "" },
                      { key: "vehicle_no", label: t("vehicle_no"), value: data?.vehicle_number ? data.vehicle_number : "" },
                      { key: "fleetadmins", label: t("fleetadmins"), value: data?.fleetadmin && role === 'admin' ? data.fleetadmin : "" },
                      { key: "device_id", label: t("device_id"), value: data?.driverDeviceId ? data.driverDeviceId : "" },

                    ].map((item) =>
                      item.value ? renderGridItem(item, isRTL): null
                    )}
                </Grid>
              </Card>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={12} md={6} lg={3}>
            <Grid item>
              <Card className={classes.card}>
                {renderTypography(t("customer_info"))}
                <Grid container direction="column" style={{ padding: 15 }}>
                  <Grid
                    item
                    style={{
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    {data?.customer_image ? (
                      <Avatar
                        alt="customer profile image"
                        src={data?.customer_image}
                        style={{ width: 100, height: 100, objectFit: "cover" }}
                      />
                    ) : (
                      <AccountCircleIcon sx={{ width: 100, height: 100 }} />
                    )}
                  </Grid>
                  {data &&
                    [
                      { key: "customer_id", label: t("customer_id"), value: data?.customer ? data.customer : "" },
                      {
                        key: "nombreComercial",
                        label: t("Nombre Comercial"),
                        value: data?.nombreComercial || t("no_commercial_name"),
                        style: {
                          fontWeight: 'bold',
                          color: data?.nombreComercial ? colors.BLACK : colors.RED,
                          backgroundColor: data?.nombreComercial ? colors.WHITE : colors.YELLOW,
                          padding: '2px 8px',
                          borderRadius: '4px',
                          border: `1px solid ${data?.nombreComercial ? colors.BLACK : colors.RED}`
                        }
                      },
                      {
                        key: "customer_contact",
                        label: t("customer_contact"),
                        value: data?.customer_contact ? (settings.AllowCriticalEditsAdmin
                          ? data?.customer_contact
                          : t("hidden_demo")) : ""
                      },
                      {
                        key: "customer_email",
                        label: t("customer_email"),
                        value: data?.customer_email ? (settings.AllowCriticalEditsAdmin
                          ? data?.customer_email
                          : t("hidden_demo")) : ""
                      },
                    ].map((item) =>
                      item.value ? renderGridItem(item, isRTL): null
                    )}
                </Grid>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Box>
      <Grid container spacing={2}>
            <Grid item xs={12} sm={12} md={6} lg={3}>
                <Card className={classes.card}>
                    <Typography variant="h6">Detalles de la Reserva</Typography>
                    <Typography className={classes.detailItem}>
                        {`Punto A: ${booking.pickup.add}`}
                    </Typography>
                    {renderWaypoints()}
                    <Typography className={classes.detailItem}>
                        {`Punto B: ${String.fromCharCode(65 + (booking.waypoints ? booking.waypoints.length : 0))}: ${booking.drop.add}`}
                    </Typography>
                </Card>
            </Grid>
        </Grid>
    </>
  );
}

export default BookingDetails;

