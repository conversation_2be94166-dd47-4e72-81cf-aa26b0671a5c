// Ejemplo de estructura de datos para el modal de productos de entrega
// Este archivo muestra cómo se estructuran los datos que maneja el modal

export const exampleBookingData = {
  id: "-OWShUnim0XKm_LzFaQj",
  delivery_folio: "POSTMAN-TEST-001",
  delivery_type: "MULTIPLE_ORDERS",
  delivery_route_id: "-OWShUnJTPRhYHQBeR6o",
  status: "ARRIVED",
  driver: "j3NYf7YtOLNcJUTlhUAbzkUOdUM2",
  driver_name: "<PERSON>",
  customer_name: "<PERSON>",
  totalPieces: 18,
  totalSKUs: 7,
  totalWeight: 16,
  
  // Órdenes de entrega con información básica
  delivery_orders: [
    {
      customerId: "pb9R4TC3Ocba5ivcYT5C1rlRG862",
      customerName: "Jonathan <PERSON>",
      deliveryAddress: "Av. <PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco",
      orderId: "ORD-MDQM0973-NBUT98",
      pickupAddress: "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco",
      productCount: 3
    },
    {
      customerId: "customer2_id_example",
      customerName: "Cliente 2 - María González",
      deliveryAddress: "Av. Vallarta 1234, Col. Americana, Guadalajara, Jalisco",
      orderId: "ORD-MDQM0973-QFPGGW",
      pickupAddress: "C. Álvarez del Castillo 890, Santa María, 44350 Guadalajara, Jalisco",
      productCount: 2
    },
    {
      customerId: "customer3_id_example",
      customerName: "Cliente 3 - Carlos Rodríguez",
      deliveryAddress: "Av. López Mateos Sur 2375, Jardines del Country, Guadalajara, Jalisco",
      orderId: "ORD-MDQM0998-N4N4DL",
      pickupAddress: "C. Álvarez del Castillo 890, Santa María, 44350 Guadalajara, Jalisco",
      productCount: 2
    }
  ]
};

// Ejemplo de estructura de productos completa (vendría de la ruta de delivery)
export const exampleProductsData = [
  {
    orderId: "ORD-MDQM0973-NBUT98",
    customerName: "Jonathan Ballesteros",
    deliveryAddress: "Av. Patria 1891, Puerta de Hierro, Zapopan, Jalisco",
    products: [
      {
        id: "ORD-MDQM0973-NBUT98-PROD-1",
        name: "Documentos Empresariales",
        sku: "DOC-001-GDL",
        description: "Paquete de documentos importantes para oficinas",
        quantity: 2,
        weight: 0.5,
        status: "PENDING" // PENDING, PICKED_UP, DELIVERED, PARTIAL
      },
      {
        id: "ORD-MDQM0973-NBUT98-PROD-2",
        name: "Material Promocional",
        sku: "PROMO-002-GDL",
        description: "Material publicitario para evento corporativo",
        quantity: 1,
        weight: 1.2,
        status: "PENDING"
      },
      {
        id: "ORD-MDQM0973-NBUT98-PROD-3",
        name: "Suministros de Oficina",
        sku: "OFFICE-003-GDL",
        description: "Artículos varios de oficina",
        quantity: 5,
        weight: 2.1,
        status: "PENDING"
      }
    ]
  },
  {
    orderId: "ORD-MDQM0973-QFPGGW",
    customerName: "Cliente 2 - María González",
    deliveryAddress: "Av. Vallarta 1234, Col. Americana, Guadalajara, Jalisco",
    products: [
      {
        id: "ORD-MDQM0973-QFPGGW-PROD-1",
        name: "Productos Farmacéuticos",
        sku: "FARM-003-GDL",
        description: "Medicamentos especializados para farmacia",
        quantity: 3,
        weight: 0.8,
        status: "PENDING"
      },
      {
        id: "ORD-MDQM0973-QFPGGW-PROD-2",
        name: "Equipos Médicos",
        sku: "MED-004-GDL",
        description: "Equipos médicos especializados",
        quantity: 1,
        weight: 3.5,
        status: "PENDING"
      }
    ]
  },
  {
    orderId: "ORD-MDQM0998-N4N4DL",
    customerName: "Cliente 3 - Carlos Rodríguez",
    deliveryAddress: "Av. López Mateos Sur 2375, Jardines del Country, Guadalajara, Jalisco",
    products: [
      {
        id: "ORD-MDQM0998-N4N4DL-PROD-1",
        name: "Componentes Electrónicos",
        sku: "ELEC-005-GDL",
        description: "Componentes para equipos electrónicos",
        quantity: 4,
        weight: 1.8,
        status: "PENDING"
      },
      {
        id: "ORD-MDQM0998-N4N4DL-PROD-2",
        name: "Herramientas Especializadas",
        sku: "TOOL-006-GDL",
        description: "Herramientas para mantenimiento técnico",
        quantity: 2,
        weight: 4.2,
        status: "PENDING"
      }
    ]
  }
];

// Ejemplo de estructura de datos de entrega que se guarda
export const exampleDeliveryData = {
  "ORD-MDQM0973-NBUT98-PROD-1": {
    delivered: true,
    deliveredQuantity: 2,
    totalQuantity: 2,
    notes: "Entregado en recepción del edificio"
  },
  "ORD-MDQM0973-NBUT98-PROD-2": {
    delivered: true,
    deliveredQuantity: 1,
    totalQuantity: 1,
    notes: "Material en perfecto estado"
  },
  "ORD-MDQM0973-NBUT98-PROD-3": {
    delivered: false,
    deliveredQuantity: 3,
    totalQuantity: 5,
    notes: "Entrega parcial - cliente no disponible para recibir todo"
  },
  "ORD-MDQM0973-QFPGGW-PROD-1": {
    delivered: true,
    deliveredQuantity: 3,
    totalQuantity: 3,
    notes: "Medicamentos entregados correctamente"
  },
  "ORD-MDQM0973-QFPGGW-PROD-2": {
    delivered: false,
    deliveredQuantity: 0,
    totalQuantity: 1,
    notes: "Cliente no disponible - reprogramar entrega"
  },
  "ORD-MDQM0998-N4N4DL-PROD-1": {
    delivered: true,
    deliveredQuantity: 4,
    totalQuantity: 4,
    notes: "Componentes verificados y entregados"
  },
  "ORD-MDQM0998-N4N4DL-PROD-2": {
    delivered: true,
    deliveredQuantity: 2,
    totalQuantity: 2,
    notes: "Herramientas entregadas al técnico responsable"
  }
};

// Estados posibles de los productos
export const PRODUCT_STATUS = {
  PENDING: 'PENDING',        // Pendiente de entrega
  PICKED_UP: 'PICKED_UP',    // Recogido del origen
  DELIVERED: 'DELIVERED',    // Entregado completamente
  PARTIAL: 'PARTIAL'         // Entrega parcial
};

// Función para calcular el progreso de entrega de una orden
export const calculateOrderProgress = (products, deliveryData) => {
  let totalProducts = products.length;
  let deliveredProducts = 0;
  let partialProducts = 0;
  
  products.forEach(product => {
    const delivery = deliveryData[product.id];
    if (delivery) {
      if (delivery.delivered && delivery.deliveredQuantity === delivery.totalQuantity) {
        deliveredProducts++;
      } else if (delivery.deliveredQuantity > 0) {
        partialProducts++;
      }
    }
  });
  
  return {
    total: totalProducts,
    delivered: deliveredProducts,
    partial: partialProducts,
    pending: totalProducts - deliveredProducts - partialProducts,
    percentage: Math.round((deliveredProducts / totalProducts) * 100)
  };
};

// Función para generar el resumen de entrega
export const generateDeliverySummary = (ordersWithProducts, deliveryData) => {
  let summary = {
    totalOrders: ordersWithProducts.length,
    completedOrders: 0,
    partialOrders: 0,
    pendingOrders: 0,
    totalProducts: 0,
    deliveredProducts: 0,
    partialProducts: 0,
    pendingProducts: 0
  };
  
  ordersWithProducts.forEach(order => {
    const progress = calculateOrderProgress(order.products, deliveryData);
    summary.totalProducts += progress.total;
    summary.deliveredProducts += progress.delivered;
    summary.partialProducts += progress.partial;
    summary.pendingProducts += progress.pending;
    
    if (progress.delivered === progress.total) {
      summary.completedOrders++;
    } else if (progress.delivered > 0 || progress.partial > 0) {
      summary.partialOrders++;
    } else {
      summary.pendingOrders++;
    }
  });
  
  return summary;
};
