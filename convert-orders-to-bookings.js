/**
 * Script para convertir un body de múltiples órdenes en múltiples llamadas al endpoint createBooking
 * Excluye estimatedRoute ya que createBooking calcula su propia ruta
 */

// Función para mapear tipos de vehículo
function mapVehicleType(vehicleType) {
    const vehicleMap = {
        'CAMIONETA': 'VAN',
        'SEDAN': 'SEDAN',
        'MINI VAN': 'MINI VAN',
        'VAN': 'VAN',
        'PREMIER': 'PREMIER'
    };
    return vehicleMap[vehicleType] || 'SEDAN'; // Default a SEDAN si no se encuentra
}

// Función para convertir una orden individual a formato createBooking
function convertOrderToBooking(order, vehicleType, defaultPaymentType = 'EFECTIVO') {
    return {
        cliente: order.customerId,
        solicitante: order.customerName,
        ubicacionOrigen: {
            direccion: order.pickupAddress.address
        },
        ubicacionDestino: {
            direccion: order.deliveryAddress.address
        },
        tipoVehiculo: mapVehicleType(vehicleType),
        tipoPago: defaultPaymentType,
        telefonoContacto: order.customerPhone || null,
        observaciones: order.notes || null,
        // Campos opcionales adicionales
        nombrePasajero: order.customerName,
        numeroPasajeros: 1
    };
}

// Función principal para convertir el body completo
function convertMultiOrderToBookings(multiOrderBody, defaultPaymentType = 'EFECTIVO') {
    const bookings = [];
    
    // Validar que el body tenga la estructura esperada
    if (!multiOrderBody.orders || !Array.isArray(multiOrderBody.orders)) {
        throw new Error('El body debe contener un array de orders');
    }
    
    if (!multiOrderBody.vehicle || !multiOrderBody.vehicle.type) {
        throw new Error('El body debe contener información del vehículo');
    }
    
    // Convertir cada orden a un booking
    multiOrderBody.orders.forEach((order, index) => {
        try {
            const booking = convertOrderToBooking(order, multiOrderBody.vehicle.type, defaultPaymentType);
            
            // Agregar información adicional del folio original
            if (multiOrderBody.folio) {
                booking.observaciones = booking.observaciones 
                    ? `${booking.observaciones} | Folio: ${multiOrderBody.folio} | Orden: ${index + 1}`
                    : `Folio: ${multiOrderBody.folio} | Orden: ${index + 1}`;
            }
            
            bookings.push(booking);
        } catch (error) {
            console.error(`Error procesando orden ${index + 1}:`, error);
        }
    });
    
    return bookings;
}

// Función para hacer las llamadas HTTP al endpoint createBooking
async function createMultipleBookings(multiOrderBody, endpointUrl, defaultPaymentType = 'EFECTIVO') {
    const bookings = convertMultiOrderToBookings(multiOrderBody, defaultPaymentType);
    const results = [];
    
    console.log(`Creando ${bookings.length} reservas...`);
    
    for (let i = 0; i < bookings.length; i++) {
        const booking = bookings[i];
        
        try {
            console.log(`Creando reserva ${i + 1}/${bookings.length}...`);
            
            const response = await fetch(endpointUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(booking)
            });
            
            const result = await response.json();
            
            results.push({
                orderIndex: i + 1,
                success: response.ok,
                status: response.status,
                data: result,
                originalOrder: {
                    customerId: booking.cliente,
                    customerName: booking.solicitante
                }
            });
            
            if (response.ok) {
                console.log(`✅ Reserva ${i + 1} creada exitosamente: ${result.data?.reference || 'Sin referencia'}`);
            } else {
                console.error(`❌ Error en reserva ${i + 1}:`, result.error);
            }
            
            // Pequeña pausa entre llamadas para no sobrecargar el servidor
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            console.error(`❌ Error de red en reserva ${i + 1}:`, error);
            results.push({
                orderIndex: i + 1,
                success: false,
                error: error.message,
                originalOrder: {
                    customerId: booking.cliente,
                    customerName: booking.solicitante
                }
            });
        }
    }
    
    return results;
}

// Función para mostrar un resumen de los resultados
function showResultsSummary(results) {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log('\n📊 RESUMEN DE RESULTADOS:');
    console.log(`✅ Exitosas: ${successful.length}`);
    console.log(`❌ Fallidas: ${failed.length}`);
    console.log(`📋 Total: ${results.length}`);
    
    if (successful.length > 0) {
        console.log('\n✅ RESERVAS EXITOSAS:');
        successful.forEach(result => {
            console.log(`- Orden ${result.orderIndex}: ${result.originalOrder.customerName} (${result.data?.data?.reference || 'Sin ref'})`);
        });
    }
    
    if (failed.length > 0) {
        console.log('\n❌ RESERVAS FALLIDAS:');
        failed.forEach(result => {
            console.log(`- Orden ${result.orderIndex}: ${result.originalOrder.customerName} - ${result.error || result.data?.error}`);
        });
    }
}

// Ejemplo de uso
const exampleUsage = `
// EJEMPLO DE USO:

const multiOrderBody = {
  "folio": "ENT-2024-TEST-001",
  "driver": "-OSgEoZzTKd3WlZ-xRyY",
  "vehicle": {
    "type": "CAMIONETA",
    "plate": "TEST-123"
  },
  "orders": [
    {
      "customerId": "VeSkwck41kXpdJIoDQH4sMjYror1",
      "customerName": "María García Test",
      "customerPhone": "+************",
      "pickupAddress": {
        "address": "Almacén Central - Dirección de Prueba"
      },
      "deliveryAddress": {
        "address": "Destino Final - Dirección de Prueba"
      },
      "notes": "Entrega de prueba - manejar con cuidado"
    }
  ]
};

const endpointUrl = 'https://us-central1-balle-813e3.cloudfunctions.net/createBooking';

// Convertir a bookings (sin hacer llamadas HTTP)
const bookings = convertMultiOrderToBookings(multiOrderBody, 'EFECTIVO');
console.log('Bookings generados:', JSON.stringify(bookings, null, 2));

// Crear las reservas (con llamadas HTTP)
createMultipleBookings(multiOrderBody, endpointUrl, 'EFECTIVO')
  .then(results => {
    showResultsSummary(results);
  })
  .catch(error => {
    console.error('Error general:', error);
  });
`;

// Exportar funciones para uso en Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        convertMultiOrderToBookings,
        createMultipleBookings,
        showResultsSummary,
        mapVehicleType
    };
}

console.log(exampleUsage);
